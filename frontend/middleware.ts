import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // Skip authentication for local development
  const isLocalhost = request.nextUrl.hostname === 'localhost' ||
                      request.nextUrl.hostname === '127.0.0.1' ||
                      request.nextUrl.hostname === '0.0.0.0';

  if (process.env.NEXT_PUBLIC_SKIP_AUTH === 'true' || isLocalhost) {
    return NextResponse.next();
  }

  // For production, you can add authentication logic here if needed
  // For now, just pass through all requests
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
}; 