'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { CTASection } from '@/components/home/<USER>/cta-section';
// import { FAQSection } from "@/components/sections/faq-section";
import { FooterSection } from '@/components/home/<USER>/footer-section';
import { HeroSection } from '@/components/home/<USER>/hero-section';
import { OpenSourceSection } from '@/components/home/<USER>/open-source-section';
import { PricingSection } from '@/components/home/<USER>/pricing-section';
import { UseCasesSection } from '@/components/home/<USER>/use-cases-section';
import { ModalProviders } from '@/providers/modal-providers';

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    // For local development, redirect to test page to bypass complex auth issues
    const isLocalhost = typeof window !== 'undefined' &&
                        (window.location.hostname === 'localhost' ||
                         window.location.hostname === '127.0.0.1' ||
                         window.location.hostname === '0.0.0.0');

    if (process.env.NEXT_PUBLIC_SKIP_AUTH === 'true' ||
        process.env.NODE_ENV === 'development' ||
        process.env.NEXT_PUBLIC_ENV_MODE === 'LOCAL' ||
        isLocalhost) {
      router.push('/test');
      return;
    }
  }, [router]);
  return (
    <>
      <ModalProviders />
      <main className="flex flex-col items-center justify-center min-h-screen w-full">
        <div className="w-full divide-y divide-border">
          <HeroSection />
          <UseCasesSection />
          {/* <CompanyShowcase /> */}
          {/* <BentoSection /> */}
          {/* <QuoteSection /> */}
          {/* <FeatureSection /> */}
          {/* <GrowthSection /> */}
          <OpenSourceSection />
          <div className='flex flex-col items-center px-4'>
            <PricingSection />
          </div>
          {/* <TestimonialSection /> */}
          {/* <FAQSection /> */}
          <CTASection />
          <FooterSection />
        </div>
      </main>
    </>
  );
}
